from uuid import UUI<PERSON>

from fastapi import APIRouter, Body, Depends
from fastapi_injector import Injected
from starlette import status

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.api.constants import DataServicePrefixes, DocumentEndpointRoutes
from services.data_service.api.queries.document_query_api import DocumentQueryAPI
from services.data_service.application.use_cases.user_data.delete_user_data_use_case import DeleteUserDataUseCase

document_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.DOCUMENT}",
    tags=["document"],
    responses={404: {"description": "Not found"}},
)


@document_router.delete(
    DocumentEndpointRoutes.BY_QUERY,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Delete user documents by query",
    description="Schedules asynchronous deletion of user's V3 documents matching the specified query criteria",
    response_description="Deletion task scheduled successfully. Returns empty response with 202 status.",
)
async def delete_user_data_by_query_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    query: DocumentQueryAPI | None = Body(
        default=None,
        description="Query criteria to filter documents for deletion. If null, deletes all user documents.",
        examples={
            "delete_specific_types": {
                "summary": "Delete specific document types",
                "description": "Delete all use cases and plans for the user",
                "value": {
                    "queries": [
                        {
                            "types": ["UseCase", "Plan"],
                            "query": None
                        }
                    ]
                }
            },
            "delete_with_field_filter": {
                "summary": "Delete documents with field criteria",
                "description": "Delete archived use cases created before a specific date",
                "value": {
                    "queries": [
                        {
                            "types": ["UseCase"],
                            "query": {
                                "type": "and",
                                "queries": [
                                    {
                                        "type": "exists",
                                        "field_name": "archived_at"
                                    },
                                    {
                                        "type": "range",
                                        "field_name": "created_at",
                                        "lt": "2024-01-01T00:00:00Z"
                                    }
                                ]
                            }
                        }
                    ]
                }
            },
            "delete_by_tags": {
                "summary": "Delete documents by tags",
                "description": "Delete all documents containing specific tags",
                "value": {
                    "queries": [
                        {
                            "types": ["UseCase", "Plan", "Template"],
                            "query": {
                                "type": "values",
                                "field_name": "tags",
                                "values": ["deprecated", "old"]
                            }
                        }
                    ]
                }
            }
        }
    ),
    use_case: DeleteUserDataUseCase = Injected(DeleteUserDataUseCase),
):
    """
    Delete user documents matching specified query criteria.

    This endpoint schedules asynchronous deletion of the authenticated user's V3 documents
    that match the provided query criteria. The deletion is performed in the background,
    and the endpoint returns immediately with a 202 Accepted status.

    **Query Structure:**
    - **queries**: Array of typed queries, each targeting specific document types
    - **types**: Document types to target (UseCase, Plan, Template, Event, etc.)
    - **query**: Optional filter criteria using field-based queries and boolean operators

    **Supported Query Types:**
    - **exists**: Check if a field exists
    - **values**: Match specific field values
    - **pattern**: Text pattern matching
    - **range**: Numeric or date range filtering
    - **radius**: Geographic radius queries

    **Boolean Operators:**
    - **and**: All conditions must match
    - **or**: Any condition must match
    - **not**: Conditions must not match

    **Security:**
    - Only deletes documents owned by the authenticated user
    - User UUID is automatically added to all queries for security
    - Cannot delete documents from other users

    **Behavior:**
    - If query is null, deletes ALL user documents (equivalent to /all_data endpoint)
    - Returns immediately after scheduling the deletion task
    - Actual deletion happens asynchronously in the background
    - Assets are only deleted when deleting all user data (query=null)

    **Error Responses:**
    - **409 Conflict**: Another deletion or upload operation is already in progress
    - **400 Bad Request**: Invalid query structure or field names
    - **401 Unauthorized**: Authentication required
    - **404 Not Found**: User not found
    """
    return await use_case.execute_async(user_uuid=user_uuid, query=query.to_query() if query else None)


@document_router.delete(
    DocumentEndpointRoutes.ALL_DATA,
    status_code=status.HTTP_202_ACCEPTED,
    description="schedules deletion of all of the user's V3 documents",
)
async def delete_all_user_data_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: DeleteUserDataUseCase = Injected(DeleteUserDataUseCase),
):
    return await use_case.execute_async(user_uuid=user_uuid, query=None)
